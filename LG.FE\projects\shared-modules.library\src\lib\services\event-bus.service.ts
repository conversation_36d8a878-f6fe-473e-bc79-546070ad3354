import { AfterViewInit, Injectable, Injector, inject, signal } from '@angular/core';
import { Subject, Subscription } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { untilDestroyed } from '../helpers/until-destroyed';
import { IUserRole } from '../models/general.model';
import { IGenderEnum, IGetStudentsRequest } from '../GeneratedTsFiles';

// Define Events Enum
export enum Events {
  StudentRegistered,
  StudentGroupAdded,
  StudentGroupEdited,
  StudentGroupRemoved,
  StudentGroupNewMemberAdded,
  ParentImpersonateStarted,
  ParentImpersonateStopped,
  CartItemAdded,
  UserLoggedIn,
  NavigateParentStudentOverview,
  StateLoadNativeLanguages,
  StateLoadTeachingLanguages,
  StateLoadParentStudents,
  StateLoadParentStudentsGroups,
  StateLoadStartImpersonate,
  StateLoadStopImpersonate,
  StateLoadGetBasket,
  StateLoadGeoLocationData,
  StateLoadCountries,
  StateLoadTimezones,
  StateLoadDialCodes,
  StateLoadParentDashboard,
  StateLoadStudentDashboard,
  StateLoadUserProfilePhoto,
  StateLoadProfileInfo,
  LoginResetRequested,
  StateLoadGetDaysOff,
}

// Define Event Data Classes
export class StudentGroupAddedEventData {
  constructor(public name?: string) { }
}

export class UserEventData {
  constructor(public user: Record<string, unknown> = {}, public hasSetPassword = false, public redirectUrl?: string) { }
}

export class UserProfileInfoEventData {
  constructor(public userId?: string, public hasSetPassword?: boolean) { }
}

export class StateLoadStartImpersonateEventData {
  constructor(public impersonateStudentId: string, public parentRefreshToken: string) { }
}

export class StateLoadStopImpersonateEventData {
  constructor(public studentRefreshToken: string) { }
}

export class StateLoadGetBacketEventData {
  constructor(public parentId: string) { }
}

export class StateLoadDashboardData {
  constructor(public role?: IUserRole, public shouldGetMoreData?: boolean) { }
}

export class DefaultGetStudentsRequest implements IGetStudentsRequest {
  pageNumber: number = 1;
  pageSize: number = 1000;
  gender: IGenderEnum = {} as IGenderEnum;
  studentAgesMin: number = 0;
  studentAgesMax: number = 17;

  constructor(initialData?: Partial<IGetStudentsRequest>) {
      Object.assign(this, initialData);
  }
}


class CustomerChangedEventData {
  constructor(public age: number) { }
}

// Define Event Payload Map
export type EventPayloadMap = {
  [Events.StudentRegistered]: undefined;
  [Events.StudentGroupAdded]: StudentGroupAddedEventData;
  [Events.StudentGroupEdited]: undefined;
  [Events.StudentGroupRemoved]: undefined;
  [Events.StudentGroupNewMemberAdded]: undefined;
  [Events.ParentImpersonateStarted]: UserEventData;
  [Events.ParentImpersonateStopped]: undefined;
  [Events.CartItemAdded]: undefined;
  [Events.UserLoggedIn]: UserEventData;
  [Events.NavigateParentStudentOverview]: undefined,
  [Events.StateLoadNativeLanguages]: undefined,
  [Events.StateLoadTeachingLanguages]: undefined,
  [Events.StateLoadParentStudents]: DefaultGetStudentsRequest,
  [Events.StateLoadParentStudentsGroups]: undefined,
  [Events.StateLoadStartImpersonate]: StateLoadStartImpersonateEventData,
  [Events.StateLoadStopImpersonate]: StateLoadStopImpersonateEventData,
  [Events.StateLoadGetBasket]: StateLoadGetBacketEventData,
  [Events.StateLoadGeoLocationData]: {},
  [Events.StateLoadCountries]: undefined,
  [Events.StateLoadTimezones]: undefined,
  [Events.StateLoadDialCodes]: undefined,
  [Events.StateLoadParentDashboard]: StateLoadDashboardData,
  [Events.StateLoadStudentDashboard]: undefined,
  [Events.StateLoadUserProfilePhoto]: undefined,
  [Events.StateLoadProfileInfo]: UserProfileInfoEventData,
  [Events.LoginResetRequested]: undefined,
  [Events.StateLoadGetDaysOff]: undefined,
};

// Map Event Names to Enum Values
export const EventNames = {
  [Events.StudentRegistered]: 'StudentRegistered',
  [Events.StudentGroupAdded]: 'StudentGroupAdded',
  [Events.StudentGroupEdited]: 'StudentGroupEdited',
  [Events.StudentGroupRemoved]: 'StudentGroupRemoved',
  [Events.StudentGroupNewMemberAdded]: 'StudentGroupNewMemberAdded',
  [Events.ParentImpersonateStarted]: 'ParentImpersonateStarted',
  [Events.ParentImpersonateStopped]: 'ParentImpersonateStopped',
  [Events.CartItemAdded]: 'CartItemAdded',
  [Events.UserLoggedIn]: 'UserLoggedIn',
  [Events.NavigateParentStudentOverview]: 'NavigateParentStudentOverview',
  [Events.StateLoadNativeLanguages]: 'StateLoadNativeLanguages',
  [Events.StateLoadTeachingLanguages]: 'StateLoadTeachingLanguages',
  [Events.StateLoadParentStudents]: 'StateLoadParentStudents',
  [Events.StateLoadParentStudentsGroups]: 'StateLoadParentStudentsGroups',
  [Events.StateLoadStartImpersonate]: 'StateLoadStartImpersonate',
  [Events.StateLoadStopImpersonate]: 'StateLoadStopImpersonate',
  [Events.StateLoadGetBasket]: 'StateLoadGetBasket',
  [Events.StateLoadGeoLocationData]: 'StateLoadGeoLocationData',
  [Events.StateLoadCountries]: 'StateLoadCountries',
  [Events.StateLoadTimezones]: 'StateLoadTimezones',
  [Events.StateLoadDialCodes]: 'StateLoadDialCodes',
  [Events.StateLoadParentDashboard]: 'StateLoadParentDashboard',
  [Events.StateLoadStudentDashboard]: 'StateLoadStudentDashboard',
  [Events.StateLoadUserProfilePhoto]: 'StateLoadUserProfilePhoto',
  [Events.StateLoadProfileInfo]: 'StateLoadProfileInfo',
  [Events.LoginResetRequested]: 'LoginResetRequested',
  [Events.StateLoadGetDaysOff]: 'StateLoadGetDaysOff',

} as const;

// Define EmitEvent Class
export class EmitEvent<T extends Events> {
  constructor(public name: T, public value: EventPayloadMap[T]) { }

  get eventName(): string {
    return EventNames[this.name];
  }
}

@Injectable({
  providedIn: 'root'
})
export class EventBusService implements AfterViewInit {
  private injector = inject(Injector);
  private untilDestroyed = untilDestroyed();
  private subscriptions: Map<Events, Subscription> = new Map();
  private eventQueue: EmitEvent<Events>[] = [];
  private eventSignal = signal<EmitEvent<Events> | null>(null);
  private eventSubject = new Subject<EmitEvent<Events>>();

  ngAfterViewInit(): void {
    throw new Error('Method not implemented.');
  }

  readEventName(name: Events): string {
    return EventNames[name];
  }

  public emit<T extends Events>(event: EmitEvent<T>): void {
    console.debug(`Emitting event: ${event.eventName}`, event.value);
    this.eventSubject.next(event);
  }

  public on<T extends Events>(event: T, action: (payload: EventPayloadMap[T]) => void): Subscription {
    const subscription = this.eventSubject.pipe(
      filter((e: EmitEvent<Events>): e is EmitEvent<T> => e.name === event),
      map((e: EmitEvent<T>) => e.value),
      this.untilDestroyed()
    ).subscribe(action);

    this.subscriptions.set(event, subscription);
    return subscription;
  }

  public off(event: Events): void {
    const subscription = this.subscriptions.get(event);
    if (subscription) {
      subscription.unsubscribe();
      this.subscriptions.delete(event);
    }
  }
}