import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, Injector, signal, type OnInit, DestroyRef } from '@angular/core';
import { toObservable, takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';
import { Router, ActivatedRoute, NavigationEnd, Params } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { DataViewModule } from 'primeng/dataview';
import { IconField } from 'primeng/iconfield';
import { InputIcon } from 'primeng/inputicon';
import { InputTextModule } from 'primeng/inputtext';
import { PaginatorModule } from 'primeng/paginator';
import {
  DefaultGetStudentsRequest,
  EmitEvent,
  EventBusService,
  Events,
  IGetStudentsResponse,
  IGetStudentsRequest,
  ISearchStudentDto,
  GeneralService,
  AuthStateService,
  DataApiStateService,
  State
} from 'SharedModules.Library';
import { IUserRole } from 'SharedModules.Library';
import { RegisterService } from '@platform.src/app/core/services/register.service';
import { ScrollPositionService } from '@platform.src/app/core/services/scroll-position.service';
import { StudentsListHelperService } from '@platform.src/app/core/services/students-list-helper.service';
import { EmptyDataImageTextComponent } from '@platform.src/app/shared/components/empty-data-image-text/empty-data-image-text.component';
import { StudentMiniInfoCardComponent } from '@platform.src/app/shared/dashboard/student-mini-info-card/student-mini-info-card.component';
import { InnerHeaderCardComponent } from '@platform.src/app/shared/layout/inner-header-card/inner-header-card.component';
import { debounceTime, distinctUntilChanged } from 'rxjs';
import { SkeletonLoaderComponent } from 'SharedModules.Library';

interface PageEvent {
  first?: number | undefined;
  rows?: number | undefined;
  page?: number | undefined;
  pageCount?: number | undefined;
}

@Component({
  selector: 'app-overview-students',
  imports: [
    CommonModule,
    ButtonModule,
    FormsModule,
    InputTextModule,
    DataViewModule,
    PaginatorModule,
    StudentMiniInfoCardComponent,
    InnerHeaderCardComponent,
    EmptyDataImageTextComponent,
    InputIcon,
    IconField,
    SkeletonLoaderComponent,
  ],
  templateUrl: './overview-students.component.html',
  styleUrl: './overview-students.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OverviewStudentsComponent implements OnInit {
  breadcrumbs = [{ label: 'Students', url: '' }];
  private injector = inject(Injector);
  private destroyRef = inject(DestroyRef);
  IUserRole = IUserRole;

  // Injected services
  registerService = inject(RegisterService);
  generalService = inject(GeneralService);
  dataStateService = inject(DataApiStateService);
  scrollPositionService = inject(ScrollPositionService);
  authService = inject(AuthStateService);
  eventBusService = inject(EventBusService);
  router = inject(Router);
  activatedRoute = inject(ActivatedRoute);
  studentsHelperService = inject(StudentsListHelperService);

  // Query parameters signal for backend pagination
  queryParams = signal<IGetStudentsRequest>(this.studentsHelperService.createDefaultStudentsRequest());

  // Search signals
  searchQuery = signal('');          // Immediate search input
  private isInitialized = signal(false); // Flag to prevent duplicate API calls during init
  private isUpdatingFromUrl = signal(false); // Flag to prevent search subscription during URL updates

  // Computed properties for backend pagination
  userToSignal = this.authService.userDecodedJWTData$;
  students$ = computed(() => this.dataStateService.parentStudents.state() || {} as State<IGetStudentsResponse>);

  // Backend pagination computed properties
  studentsData = computed(() => {
    const response: IGetStudentsResponse = this.students$().data || {} as IGetStudentsResponse;
    return response.pageData as ISearchStudentDto[] || [] as ISearchStudentDto[];
  });

  totalRecords = computed(() => {
    const response: IGetStudentsResponse = this.students$().data || {} as IGetStudentsResponse;
    return response.totalRecords || 0;
  });

  currentPage = computed(() => this.queryParams().pageNumber);
  pageSize = computed(() => this.queryParams().pageSize);
  first = computed(() => (this.currentPage() - 1) * this.pageSize());

  // For PrimeNG paginator compatibility
  rows = computed(() => this.queryParams().pageSize);

  // Loading state
  isLoading = computed(() => this.students$().loading || false);

  ngOnInit(): void {
    this.scrollPositionService.restoreScrollPosition(this.router.url);

    // Listen to route query parameter changes (this handles both initial load and URL changes)
    this.activatedRoute.queryParams
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((params) => {
        this.handleQueryParamsChange(params);
      });

    // Handle search query updates with debouncing (only after initialization and not during URL updates)
    toObservable(this.searchQuery, { injector: this.injector })
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe((query) => {
        // Only trigger search updates after component is initialized and not during URL parameter updates
        if (this.isInitialized() && !this.isUpdatingFromUrl()) {
          this.updateSearchTerm(query);
        }
      });
  }

  onPageChange(event: PageEvent) {
    const newPage = (event.page ?? 0) + 1; // Convert 0-based page to 1-based
    const newPageSize = event.rows || this.pageSize();

    if (newPage !== this.currentPage() || newPageSize !== this.pageSize()) {
      this.updateQueryParams({
        pageNumber: newPage,
        pageSize: newPageSize
      });
    }
  }

  onSearchInputChange(event: Event) {
    const value = (event.target as HTMLInputElement)?.value || '';
    this.searchQuery.set(value);
  }

  /**
   * Handle query parameter changes from URL
   */
  private handleQueryParamsChange(params: Params): void {
    console.log('🔄 URL params changed:', params);

    // Set flag to prevent search subscription from triggering during URL updates
    this.isUpdatingFromUrl.set(true);

    const request = this.studentsHelperService.mapQueryParamsToStudentsRequest(params);
    console.log('📋 Mapped request:', request);

    // Set parent ID for the request
    const parentId = this.authService.getUserClaims()?.id;
    if (parentId) {
      request.parentId = parentId;
    }

    this.queryParams.set(request);
    this.searchQuery.set(request.searchTerm || '');
    this.loadStudentsData();

    // Mark as initialized after first load
    if (!this.isInitialized()) {
      this.isInitialized.set(true);
      console.log('✅ Component initialized');
    }

    // Reset flag after updates are complete
    this.isUpdatingFromUrl.set(false);
  }

  /**
   * Update search term and trigger backend search
   */
  private updateSearchTerm(query: string): void {
    this.updateQueryParams({
      searchTerm: query.trim() || null,
      pageNumber: 1 // Reset to first page on search
    });
  }

  /**
   * Update query parameters (URL change will trigger API call via route subscription)
   */
  private updateQueryParams(updates: Partial<IGetStudentsRequest>): void {
    console.log('🔄 Updating query params:', updates);
    this.queryParams.update(current => ({ ...current, ...updates }));
    const newParams = this.queryParams();
    console.log('📤 New query params:', newParams);

    this.generalService.updateQueryParams(newParams, {
      replaceUrl: false, // Create browser history entries for navigation back/forward
      queryParamsHandling: 'merge'
    });
    // Note: API call will be triggered by the route queryParams subscription
  }

  /**
   * Load students data using current query parameters
   */
  private loadStudentsData(): void {
    this.eventBusService.emit(new EmitEvent(Events.StateLoadParentStudents, this.queryParams()));
  }
}