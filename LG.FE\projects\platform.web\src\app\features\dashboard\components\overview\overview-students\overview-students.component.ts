import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, Injector, signal, type OnInit, DestroyRef } from '@angular/core';
import { toObservable, takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';
import { Router, ActivatedRoute, NavigationEnd, Params } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { DataViewModule } from 'primeng/dataview';
import { IconField } from 'primeng/iconfield';
import { InputIcon } from 'primeng/inputicon';
import { InputTextModule } from 'primeng/inputtext';
import { PaginatorModule } from 'primeng/paginator';
import {
  DefaultGetStudentsRequest,
  EmitEvent,
  EventBusService,
  Events,
  IGetStudentsResponse,
  IGetStudentsRequest,
  ISearchStudentDto,
  GeneralService,
  AuthStateService,
  DataApiStateService,
  State
} from 'SharedModules.Library';
import { IUserRole } from 'SharedModules.Library';
import { RegisterService } from '@platform.src/app/core/services/register.service';
import { ScrollPositionService } from '@platform.src/app/core/services/scroll-position.service';
import { StudentsListHelperService } from '@platform.src/app/core/services/students-list-helper.service';
import { EmptyDataImageTextComponent } from '@platform.src/app/shared/components/empty-data-image-text/empty-data-image-text.component';
import { StudentMiniInfoCardComponent } from '@platform.src/app/shared/dashboard/student-mini-info-card/student-mini-info-card.component';
import { InnerHeaderCardComponent } from '@platform.src/app/shared/layout/inner-header-card/inner-header-card.component';
import { debounceTime, distinctUntilChanged, skip } from 'rxjs';
import { SkeletonLoaderComponent } from 'SharedModules.Library';

interface PageEvent {
  first?: number | undefined;
  rows?: number | undefined;
  page?: number | undefined;
  pageCount?: number | undefined;
}

@Component({
  selector: 'app-overview-students',
  imports: [
    CommonModule,
    ButtonModule,
    FormsModule,
    InputTextModule,
    DataViewModule,
    PaginatorModule,
    StudentMiniInfoCardComponent,
    InnerHeaderCardComponent,
    EmptyDataImageTextComponent,
    InputIcon,
    IconField,
    SkeletonLoaderComponent,
  ],
  templateUrl: './overview-students.component.html',
  styleUrl: './overview-students.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OverviewStudentsComponent implements OnInit {
  breadcrumbs = [{ label: 'Students', url: '' }];
  private injector = inject(Injector);
  private destroyRef = inject(DestroyRef);
  IUserRole = IUserRole;

  // Injected services
  registerService = inject(RegisterService);
  generalService = inject(GeneralService);
  dataStateService = inject(DataApiStateService);
  scrollPositionService = inject(ScrollPositionService);
  authService = inject(AuthStateService);
  eventBusService = inject(EventBusService);
  router = inject(Router);
  activatedRoute = inject(ActivatedRoute);
  studentsHelperService = inject(StudentsListHelperService);

  // Query parameters signal for backend pagination
  queryParams = signal<IGetStudentsRequest>(this.studentsHelperService.createDefaultStudentsRequestForParent());

  // Search signals
  searchQuery = signal('');          // Immediate search input
  private hasLoadedInitialData = signal(false); // Flag to track if we've loaded data once

  // Computed properties for backend pagination
  userToSignal = this.authService.userDecodedJWTData$;
  students$ = computed(() => this.dataStateService.parentStudents.state() || {} as State<IGetStudentsResponse>);

  // Backend pagination computed properties
  studentsData = computed(() => {
    const response: IGetStudentsResponse = this.students$().data || {} as IGetStudentsResponse;
    return response.pageData as ISearchStudentDto[] || [] as ISearchStudentDto[];
  });

  totalRecords = computed(() => {
    const response: IGetStudentsResponse = this.students$().data || {} as IGetStudentsResponse;
    return response.totalRecords || 0;
  });

  currentPage = computed(() => this.queryParams().pageNumber);
  pageSize = computed(() => this.queryParams().pageSize);
  first = computed(() => (this.currentPage() - 1) * this.pageSize());

  // For PrimeNG paginator compatibility
  rows = computed(() => this.queryParams().pageSize);

  // Loading state
  isLoading = computed(() => this.students$().loading || false);

  ngOnInit(): void {
    this.scrollPositionService.restoreScrollPosition(this.router.url);

    // Setup subscriptions first (they will be blocked by the flag)
    this.setupUrlSubscription();
    this.setupSearchSubscription();

    // Then load initial data (this will set the flag to allow subsequent changes)
    this.loadInitialData();
  }

  /**
   * Load initial data based on current URL parameters
   */
  private loadInitialData(): void {
    const params = this.activatedRoute.snapshot.queryParams;
    const request = this.studentsHelperService.mapQueryParamsToStudentsRequest(params);

    // Set parent ID
    const parentId = this.authService.getUserClaims()?.id;
    if (parentId) {
      request.parentId = parentId;
    }

    // Update component state
    this.queryParams.set(request);
    this.searchQuery.set(request.searchTerm || '');

    // Clean request to remove null/undefined values before API call
    const cleanRequest = this.cleanRequestForApi(request);

    // Make single API call
    this.eventBusService.emit(new EmitEvent(Events.StateLoadParentStudents, cleanRequest));

    // Mark as loaded AFTER everything is complete to enable subscriptions
    this.hasLoadedInitialData.set(true);
  }

  /**
   * Setup URL parameter subscription for subsequent changes
   */
  private setupUrlSubscription(): void {
    this.activatedRoute.queryParams
      .pipe(
        skip(1), // Skip the initial emission to prevent duplicate API call
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe((params: Params) => {
        const request = this.studentsHelperService.mapQueryParamsToStudentsRequest(params);

        // Set parent ID
        const parentId = this.authService.getUserClaims()?.id;
        if (parentId) {
          request.parentId = parentId;
        }

        // Update component state
        this.queryParams.set(request);
        this.searchQuery.set(request.searchTerm || '');

        // Clean request to remove null/undefined values before API call
        const cleanRequest = this.cleanRequestForApi(request);

        // Make API call for new data
        this.eventBusService.emit(new EmitEvent(Events.StateLoadParentStudents, cleanRequest));
      });
  }

  /**
   * Setup search input subscription
   */
  private setupSearchSubscription(): void {
    toObservable(this.searchQuery, { injector: this.injector })
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe((query) => {
        // Only process search after initial load and if it's a user-initiated change
        if (!this.hasLoadedInitialData()) {
          return;
        }

        // Check if this is a user change vs programmatic change
        const currentSearchTerm = this.queryParams().searchTerm || '';
        if (query === currentSearchTerm) {
          return;
        }

        this.handleUserSearch(query);
      });
  }

  onPageChange(event: PageEvent) {
    const newPage = (event.page ?? 0) + 1; // Convert 0-based page to 1-based
    const newPageSize = event.rows || this.pageSize();

    if (newPage !== this.currentPage() || newPageSize !== this.pageSize()) {
      this.updateUrlWithParams({
        pageNumber: newPage,
        pageSize: newPageSize
      });
    }
  }

  onSearchInputChange(event: Event) {
    const value = (event.target as HTMLInputElement)?.value || '';
    this.searchQuery.set(value);
  }

  /**
   * Handle user-initiated search
   */
  private handleUserSearch(query: string): void {
    this.updateUrlWithParams({
      searchTerm: query.trim() || null,
      pageNumber: 1 // Reset to first page on search
    });
  }

  /**
   * Update URL with new parameters (will trigger URL subscription)
   */
  private updateUrlWithParams(updates: Partial<IGetStudentsRequest>): void {
    const newParams = { ...this.queryParams(), ...updates };

    this.generalService.updateQueryParams(newParams, {
      replaceUrl: true,
    });
  }

  /**
   * Clean request object by removing null, undefined, and empty string values
   * Only sends parameters that have actual values to the API
   */
  private cleanRequestForApi(request: IGetStudentsRequest): any {
    const cleanRequest: any = {};

    // Always include required properties
    cleanRequest.pageNumber = request.pageNumber;
    cleanRequest.pageSize = request.pageSize;
    cleanRequest.gender = request.gender;
    cleanRequest.studentAgesMin = request.studentAgesMin;
    cleanRequest.studentAgesMax = request.studentAgesMax;

    // Only include optional properties that have actual values
    for (const [key, value] of Object.entries(request)) {
      // Skip required properties (already added above)
      if (['pageNumber', 'pageSize', 'gender', 'studentAgesMin', 'studentAgesMax'].includes(key)) {
        continue;
      }

      if (value !== null && value !== undefined && value !== '') {
        // Special handling for different types
        if (typeof value === 'number' || typeof value === 'boolean') {
          cleanRequest[key] = value;
        } else if (value instanceof Date) {
          cleanRequest[key] = value;
        } else if (typeof value === 'string' && value.trim() !== '') {
          cleanRequest[key] = value;
        }
      }
    }

    return cleanRequest;
  }
}