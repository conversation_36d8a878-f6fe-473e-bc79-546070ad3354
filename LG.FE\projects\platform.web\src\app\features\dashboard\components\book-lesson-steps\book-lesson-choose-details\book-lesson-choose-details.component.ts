import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, signal, type OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { StepperModule } from 'primeng/stepper';
import { DefaultGetStudentsRequest, EmitEvent, EventBusService, Events, Severity } from 'SharedModules.Library';
import { DataApiStateService } from 'SharedModules.Library';
import { GeneralService } from 'SharedModules.Library';
import { CardSplitLayoutComponent } from '@platform.src/app/shared/components/card-split-layout/card-split-layout.component';
import { EmptyDataImageTextComponent } from '@platform.src/app/shared/components/empty-data-image-text/empty-data-image-text.component';

import { PrimeStudentGroupSelectionComponent } from '@platform.src/app/shared/components/prime/prime-student-group-selection/prime-student-group-selection.component';
import { PrimeStudentsSelectionComponent } from '@platform.src/app/shared/components/prime/prime-students-selection/prime-students-selection.component';
import { BottomBarStepButtonsComponent } from '@platform.src/app/features/dashboard/bottom-bar-step-buttons/bottom-bar-step-buttons.component';
import { PackageMiniInfoCardComponent } from '@platform.app/shared/dashboard/package-mini-info-card/package-mini-info-card.component';

@Component({
  selector: 'app-book-lesson-choose-details',
  imports: [CommonModule,
    ButtonModule,
    CardSplitLayoutComponent,
    RouterModule,
    StepperModule,
    CardSplitLayoutComponent,
    EmptyDataImageTextComponent,
    BottomBarStepButtonsComponent,
    PrimeStudentsSelectionComponent,
    PrimeStudentGroupSelectionComponent,
    PackageMiniInfoCardComponent,
  ],
  templateUrl: './book-lesson-choose-details.component.html',
  styleUrl: './book-lesson-choose-details.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class BookLessonChooseDetailsComponent implements OnInit {

  Severity = Severity;
  resetStudentsSelectionSignal = signal(false);
  generalService = inject(GeneralService);
  dataStateService = inject(DataApiStateService);
  private readonly eventBusService = inject(EventBusService);
  carouselItems: any[] = this.generalService.dummyPackages;
  selectedCardId: number | null = null;

  students$ = computed(() => this.dataStateService.parentStudents.state() || {});
  studentGroups$ = computed(() => this.dataStateService.parentStudentsGroups.state());
  ngOnInit(): void {
    const request = new DefaultGetStudentsRequest();
    const cleanRequest = this.cleanRequestForApi(request);
    this.eventBusService.emit(new EmitEvent(Events.StateLoadParentStudents, cleanRequest));
    this.eventBusService.emit(new EmitEvent(Events.StateLoadParentStudentsGroups, undefined));
  }


  /** Handles selection of a student */
  onSelectedStudent(student: any): void {
  }


  onPackageSelected(index: number): void {
    this.selectedCardId = this.selectedCardId === index ? null : index;
    console.log(index);
  }

  /**
   * Clean request object by removing null, undefined, and empty string values
   * Only sends parameters that have actual values to the API
   */
  private cleanRequestForApi(request: any): any {
    const cleanRequest: any = {};

    // Always include required properties
    cleanRequest.pageNumber = request.pageNumber || 1;
    cleanRequest.pageSize = request.pageSize || 10;
    cleanRequest.gender = request.gender !== undefined ? request.gender : 0;
    cleanRequest.studentAgesMin = request.studentAgesMin !== undefined ? request.studentAgesMin : 0;
    cleanRequest.studentAgesMax = request.studentAgesMax !== undefined ? request.studentAgesMax : 17;

    // Only include optional properties that have actual values
    for (const [key, value] of Object.entries(request)) {
      // Skip required properties (already added above)
      if (['pageNumber', 'pageSize', 'gender', 'studentAgesMin', 'studentAgesMax'].includes(key)) {
        continue;
      }

      if (value !== null && value !== undefined && value !== '') {
        // Special handling for different types
        if (typeof value === 'number' || typeof value === 'boolean') {
          cleanRequest[key] = value;
        } else if (value instanceof Date) {
          cleanRequest[key] = value;
        } else if (typeof value === 'string' && value.trim() !== '') {
          cleanRequest[key] = value;
        }
      }
    }

    return cleanRequest;
  }
}
